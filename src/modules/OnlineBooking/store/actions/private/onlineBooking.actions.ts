/*
 * @since 2020-11-11 11:22:20
 * <AUTHOR> <<EMAIL>>
 */

import {
  type GetBookingRequestItem,
  type GetBookingRequestListResponse,
} from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Overwrite } from 'utility-types';
import { OpenApiModels } from '../../../../../types/openApi/schema';
import { id2NumberAndRequired, overwriteParamsType } from '../../../../../utils/api/api';
import { BookingRequestServiceClient } from '../../../../../utils/clients';
import { EnumValues } from '../../../../../utils/createEnum';
import { http } from '../../../../../utils/http';
import { action } from '../../../../../utils/store/action';
import { createLoadListRPCAction } from '../../../../../utils/store/createLoadListRPCAction';
import { createOnlineBookingObservableAction } from '../../../../../utils/store/observableServices/observableServices';
import { PAGE_SIZE, copyFilter } from '../../../../../utils/store/utils';
import { currentBusinessIdBox } from '../../../../Business/store/business.boxes';
import { GroomingTicketStatus, groomingTicketMapBox } from '../../../../Grooming/store/grooming_ticket.boxes';
import {
  ActionType,
  SendNotifyType,
} from '../../../OnlineBookingLatestRequestDetail/components/OnlineBookingRequestActionModal.props';
import {
  OnlineBookingRequestListType,
  OverviewResProps,
  businessOnlineBookingRequestListBox,
  onlineBookingListKey,
} from '../../onlineBooking.boxes';

export type BookOnlineInfo = OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineInfo'];

type GetBookingRequestListResponseMobileFormat =
  OpenApiModels['GET/grooming/bookOnline/appointment/v2']['Res']['data']['list'];

// 数据转换函数：将Web端API响应转换为移动端期望的格式
function transformWebApiToMobileFormat(
  webResponse: GetBookingRequestListResponse,
): GetBookingRequestListResponseMobileFormat {
  return webResponse.bookingRequestItems.map((item: GetBookingRequestItem) => ({
    groomingId: parseInt(item.bookingRequest.id),
    customerFirstName: item.customerDetail.firstName,
    customerLastName: item.customerDetail.lastName,
    createTime: item.bookingRequest.createdAt
      ? Math.floor(new Date(item.bookingRequest.createdAt).getTime() / 1000)
      : 0,
    appointmentDate: item.bookingRequest.startDate,
    appointmentStartTime: item.bookingRequest.startTime,
    appointmentEndTime: item.bookingRequest.endTime,
    staffId: item.bookingRequest.staffId ? parseInt(item.bookingRequest.staffId) : 0,
    staffFirstName: item.bookingRequest.staffName?.split(' ')[0] || '',
    staffLastName: item.bookingRequest.staffName?.split(' ').slice(1).join(' ') || '',
    status: item.bookingRequest.status,
    bookOnlineStatus: item.bookingRequest.status, // 使用相同的状态值
    isPaid: item.bookingRequest.isPrepaid ? 1 : 0,
    enablePreAuth: false, // Web端API暂时没有这个字段，默认为false
    phoneNumber: item.customerDetail.phoneNumber,
    customerId: parseInt(item.customerDetail.customerId),
    businessId: parseInt(item.customerDetail.businessId),
    isNewCustomer: false, // Web端API暂时没有这个字段，默认为false
    hasRequestUpdate: item.hasRequestUpdate,
    waitListDTO: null, // Web端API暂时没有这个字段
    petList:
      item.serviceDetails?.flatMap((serviceDetail) =>
        serviceDetail.services.map((service) => {
          // 根据 serviceItemType 获取对应的服务信息
          let serviceId = '';
          let serviceName = '';

          // serviceItemType 是数字: 1=GROOMING, 2=BOARDING, 3=DAYCARE, 4=EVALUATION
          switch (service.serviceItemType) {
            case 1: // GROOMING
              serviceId = service.grooming?.service?.serviceId || '';
              serviceName = service.grooming?.service?.serviceName || '';
              break;
            case 2: // BOARDING
              serviceId = service.boarding?.service?.serviceId || '';
              serviceName = service.boarding?.service?.serviceName || '';
              break;
            case 3: // DAYCARE
              serviceId = service.daycare?.service?.serviceId || '';
              serviceName = service.daycare?.service?.serviceName || '';
              break;
            case 4: // EVALUATION
              serviceId = service.evaluation?.service?.serviceId || '';
              serviceName = service.evaluation?.service?.serviceName || '';
              break;
            default:
              serviceId = '';
              serviceName = '';
          }

          return {
            petId: parseInt(serviceDetail.petDetail?.id || '0'),
            petName: serviceDetail.petDetail?.petName || '',
            serviceId: parseInt(serviceId || '0'),
            serviceName: serviceName || '',
            appointmentDate: item.bookingRequest.startDate,
          };
        }),
      ) || [],
  }));
}

export const getOnlineBookingRequestListGRPC = createLoadListRPCAction({
  request: BookingRequestServiceClient.getBookingRequestList,
  initialParams: {
    pagination: { pageSize: PAGE_SIZE, pageNum: 1 },
    orderBys: [
      {
        fieldName: 'createdAt',
        asc: false,
      },
    ],
    serviceTypeIncludes: [ServiceItemType.GROOMING],
  },
  listBox: businessOnlineBookingRequestListBox,
  selectKey: (select) => onlineBookingListKey(select(currentBusinessIdBox), OnlineBookingRequestListType.Requests),
  getList: (r) => r.bookingRequestItems.map((l) => +l.bookingRequest.appointmentId),
  mergeEntities: (webResponse) =>
    groomingTicketMapBox.mergeItems(
      id2NumberAndRequired(transformWebApiToMobileFormat(webResponse).data).list.map(copyFilter('groomingId', 'id')),
      'enablePreAuth',
    ),
});

export const cancelOnlineBookingRequest = createOnlineBookingObservableAction(
  'cancelOnlineBookingRequest',
  async (
    dispatch,
    select,
    { id, refundPrepaid = false }: { id: number; refundPrepaid: boolean },
    businessId: number = select(currentBusinessIdBox),
  ) => {
    await http.open('PUT/grooming/appointment/cancel', { id, cancelByType: 0, noShow: 2, refundPrepaid });
    const ob = select(groomingTicketMapBox.mustGetItem(id));
    console.warn('cancelOnlineBookingRequest', id, businessId, ob.isWaitingList);
    dispatch([
      groomingTicketMapBox.mergeItem(id, { status: GroomingTicketStatus.Cancelled }),
      businessOnlineBookingRequestListBox.updateItem(
        onlineBookingListKey(
          businessId,
          ob.isWaitingList ? OnlineBookingRequestListType.WaitingList : OnlineBookingRequestListType.Requests,
        ),
        (v) => v.delete(id),
      ),
    ]);
  },
);

export const moveOnlineBookingRequestToWaitingList = action(
  async (dispatch, select, id: number, businessId: number = select(currentBusinessIdBox)) => {
    await http.open('PUT/grooming/appointment/waiting', { id });
    dispatch([
      groomingTicketMapBox.mergeItem(id, { isWaitingList: 1 }),
      businessOnlineBookingRequestListBox.updateItem(
        onlineBookingListKey(businessId, OnlineBookingRequestListType.Requests),
        (v) => v.delete(id),
      ),
    ]);
  },
);

export const notifyMoveToWaitingList = action(
  async (
    dispatch,
    select,
    input: { groomingId: number; notifyType: EnumValues<typeof SendNotifyType>; type: ActionType },
  ) => {
    const { groomingId, notifyType, type } = input;
    const isSendText = [SendNotifyType.Text, SendNotifyType.TextAndEmail].includes(notifyType);
    const isSendEmail = [SendNotifyType.Email, SendNotifyType.TextAndEmail].includes(notifyType);
    await http.open('POST/message/onlineBooking/manual/notify', {
      groomingId,
      isSendEmail,
      isSendText,
      // note:
      // 目前 delete 和 decline 后端业务逻辑一致，但后端未支持 delete 字段，引发问题
      // 因此 type 为 delete 的情况改为 decline 请求
      type: type === 'delete' ? 'decline' : type,
    });
  },
);

export const getOnlineBookingOverview = createOnlineBookingObservableAction(
  'getOnlineBookingOverview',
  async (
    _dispatch,
    _select,
    input: Overwrite<
      OpenApiModels['POST/grooming/ob/v2/business/metrics']['Req'],
      {
        metrics: { name: string; types: string[] }[];
      }
    >,
  ) => {
    const res = await http.open('POST/grooming/ob/v2/business/metrics', overwriteParamsType(input));
    return res.reduce((prev, { name, ...rest }) => ({ ...prev, [name]: rest }), {} as OverviewResProps);
  },
);
